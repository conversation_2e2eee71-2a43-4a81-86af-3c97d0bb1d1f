import { createClient } from '@supabase/supabase-js';
import fs from 'fs';
import path from 'path';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.VITE_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase credentials in .env file');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function runMigration(migrationPath) {
  try {
    console.log(`\n🔄 Running migration: ${path.basename(migrationPath)}`);

    const migrationSQL = fs.readFileSync(migrationPath, 'utf8');

    // Split the migration into individual statements
    const statements = migrationSQL
      .split(';')
      .map(stmt => stmt.trim())
      .filter(stmt => stmt.length > 0 && !stmt.startsWith('--'));

    for (const statement of statements) {
      if (statement.trim()) {
        const { error } = await supabase.rpc('exec_sql', { sql: statement + ';' });
        if (error) {
          console.error(`❌ Error executing statement: ${error.message}`);
          console.error(`Statement: ${statement.substring(0, 100)}...`);
        }
      }
    }

    console.log(`✅ Migration completed: ${path.basename(migrationPath)}`);
  } catch (error) {
    console.error(`❌ Error running migration ${migrationPath}:`, error.message);
  }
}

async function runAllMigrations() {
  console.log('🚀 Starting migration process...');

  const migrationFiles = [
    'supabase/migrations/20250207000000_add_multi_tenant_support.sql',
    'supabase/migrations/20250207000001_add_business_table_policies.sql',
    'supabase/migrations/20250207000002_setup_default_organization.sql'
  ];

  for (const migrationFile of migrationFiles) {
    if (fs.existsSync(migrationFile)) {
      await runMigration(migrationFile);
    } else {
      console.warn(`⚠️  Migration file not found: ${migrationFile}`);
    }
  }

  console.log('\n🎉 All migrations completed!');
}

// Alternative method: Execute SQL directly
async function executeSQLDirectly() {
  console.log('🔄 Executing SQL directly...');

  try {
    // First, let's try to create the organizations table
    const createOrgTableSQL = `
      CREATE TABLE IF NOT EXISTS organizations (
        id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
        name TEXT NOT NULL,
        slug TEXT UNIQUE NOT NULL,
        description TEXT,
        logo_url TEXT,
        is_active BOOLEAN DEFAULT true,
        created_by UUID REFERENCES auth.users(id),
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );
    `;

    const { error: orgError } = await supabase.rpc('exec_sql', { sql: createOrgTableSQL });
    if (orgError) {
      console.error('Error creating organizations table:', orgError);
    } else {
      console.log('✅ Organizations table created');
    }

    // Add columns to users table
    const addColumnsSQL = `
      DO $$ 
      BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'organization_id') THEN
          ALTER TABLE users ADD COLUMN organization_id UUID REFERENCES organizations(id);
        END IF;
        
        IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_super_admin') THEN
          ALTER TABLE users ADD COLUMN is_super_admin BOOLEAN DEFAULT false;
        END IF;
      END $$;
    `;

    const { error: alterError } = await supabase.rpc('exec_sql', { sql: addColumnsSQL });
    if (alterError) {
      console.error('Error adding columns:', alterError);
    } else {
      console.log('✅ User table columns added');
    }

  } catch (error) {
    console.error('Error executing SQL:', error);
  }
}

// Check if we can execute SQL
async function testConnection() {
  try {
    const { data, error } = await supabase.from('users').select('count', { count: 'exact', head: true });
    if (error) {
      console.error('Connection test failed:', error);
      return false;
    }
    console.log('✅ Database connection successful');
    return true;
  } catch (error) {
    console.error('Connection test failed:', error);
    return false;
  }
}

async function main() {
  const connected = await testConnection();
  if (!connected) {
    console.log('\n📝 Manual Migration Instructions:');
    console.log('Since we cannot execute SQL directly, please run the migrations manually:');
    console.log('1. Go to your Supabase dashboard: https://supabase.com/dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Copy and paste the content of each migration file:');
    console.log('   - supabase/migrations/20250207000000_add_multi_tenant_support.sql');
    console.log('   - supabase/migrations/20250207000001_add_business_table_policies.sql');
    console.log('   - supabase/migrations/20250207000002_setup_default_organization.sql');
    console.log('4. Execute each migration in order');
    return;
  }

  // Try to run migrations
  await executeSQLDirectly();
}

main().catch(console.error);
