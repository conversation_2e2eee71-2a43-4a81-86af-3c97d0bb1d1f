# 🚀 Multi-Tenant EMS Migration Instructions

Since the Supabase CLI is not available locally, you need to run the migrations manually through the Supabase Dashboard.

## 📋 Step-by-Step Instructions

### 1. Access Supabase Dashboard
1. Go to [Supabase Dashboard](https://supabase.com/dashboard)
2. Login to your account
3. Select your project: `hwdkurbdbvknohzilubf`

### 2. Open SQL Editor
1. In the left sidebar, click on **"SQL Editor"**
2. Click **"New Query"** to create a new SQL script

### 3. Run Migrations in Order

#### Migration 1: Multi-Tenant Support
Copy and paste the content from `supabase/migrations/20250207000000_add_multi_tenant_support.sql` and execute it.

**Key changes:**
- Creates `organizations` table
- Adds `organization_id` and `is_super_admin` columns to `users` table
- Adds `organization_id` to all business tables
- Creates indexes for performance
- Updates RLS policies for multi-tenant isolation

#### Migration 2: Business Table Policies
Copy and paste the content from `supabase/migrations/20250207000001_add_business_table_policies.sql` and execute it.

**Key changes:**
- Updates RLS policies for all business tables
- Ensures organization-level data isolation
- Allows super admin access to all data

#### Migration 3: Default Organization Setup
Copy and paste the content from `supabase/migrations/20250207000002_setup_default_organization.sql` and execute it.

**Key changes:**
- Creates a default organization for existing data
- Migrates all existing users and data to the default organization
- Sets up automatic triggers for organization assignment

## 🔍 Verification Steps

After running all migrations, verify the setup:

### 1. Check Tables Created
```sql
-- Check if organizations table exists
SELECT * FROM organizations LIMIT 1;

-- Check if user table has new columns
SELECT id, email, organization_id, is_super_admin FROM users LIMIT 1;
```

### 2. Test Super Admin Creation
```sql
-- Create a test super admin user (replace with your email)
INSERT INTO auth.users (email, email_confirmed_at, created_at, updated_at)
VALUES ('<EMAIL>', now(), now(), now());

-- The trigger should automatically create the user profile
SELECT * FROM users WHERE email = '<EMAIL>';
```

### 3. Test Organization Creation
```sql
-- Create a test organization
INSERT INTO organizations (name, slug, description)
VALUES ('Test Organization', 'test-org', 'A test organization');

-- Verify it was created
SELECT * FROM organizations WHERE slug = 'test-org';
```

## 🎯 Testing the Application

After migrations are complete:

1. **Start the development server:**
   ```bash
   npm run dev
   ```

2. **Test Super Admin Access:**
   - Create a user with email ending in `@superadmin.co`
   - Login and verify you're redirected to `/super-admin`
   - Check that you can see the super admin dashboard

3. **Test Organization Isolation:**
   - Create regular users and assign them to different organizations
   - Verify they can only see their organization's data

## 🔧 Alternative: Manual Table Creation

If the migration files don't work, you can create the tables manually:

### Create Organizations Table
```sql
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  logo_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);
```

### Add Columns to Users Table
```sql
-- Add organization_id column
ALTER TABLE users ADD COLUMN IF NOT EXISTS organization_id UUID REFERENCES organizations(id);

-- Add is_super_admin column
ALTER TABLE users ADD COLUMN IF NOT EXISTS is_super_admin BOOLEAN DEFAULT false;
```

### Update User Role Enum
```sql
-- Update the user_role enum to include super_admin
ALTER TYPE user_role ADD VALUE IF NOT EXISTS 'super_admin';
```

## 🚨 Important Notes

1. **Backup First:** Always backup your database before running migrations
2. **Run in Order:** Execute migrations in the exact order specified
3. **Check Errors:** If any migration fails, fix the error before proceeding
4. **Test Thoroughly:** Test all functionality after migrations are complete

## 🆘 Troubleshooting

### Common Issues:

1. **Permission Errors:** Make sure you're using the service role key, not the anon key
2. **Table Already Exists:** Some tables might already exist - that's okay, the migrations handle this
3. **RLS Policy Conflicts:** If you get RLS policy errors, drop existing policies first

### Getting Help:

If you encounter issues:
1. Check the Supabase logs in the dashboard
2. Verify your database schema in the Table Editor
3. Test queries in the SQL Editor

## ✅ Success Indicators

You'll know the migration was successful when:
- ✅ Organizations table exists and is accessible
- ✅ Users table has `organization_id` and `is_super_admin` columns
- ✅ Super admin users can access `/super-admin` route
- ✅ Regular users can only see their organization's data
- ✅ All existing data is preserved and assigned to the default organization

---

**Need help?** The migration files are located in the `supabase/migrations/` directory and contain all the necessary SQL commands.
