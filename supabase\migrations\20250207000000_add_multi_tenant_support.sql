/*
  # Multi-Tenant Support Migration
  
  1. Create organizations table
  2. Add organization_id and is_super_admin to existing users table
  3. Add organization_id to all business tables
  4. Update RLS policies for multi-tenant isolation
  5. Create super admin and organization isolation policies
*/

-- Create organizations table
CREATE TABLE IF NOT EXISTS organizations (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  slug TEXT UNIQUE NOT NULL,
  description TEXT,
  logo_url TEXT,
  is_active BOOLEAN DEFAULT true,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Add columns to existing users table (only if they don't exist)
DO $$ 
BEGIN
  -- Add organization_id column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'organization_id') THEN
    ALTER TABLE users ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add is_super_admin column if it doesn't exist
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'users' AND column_name = 'is_super_admin') THEN
    ALTER TABLE users ADD COLUMN is_super_admin BOOLEAN DEFAULT false;
  END IF;
END $$;

-- Add organization_id to all business tables
DO $$ 
BEGIN
  -- Add to attendance_logs
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'attendance_logs' AND column_name = 'organization_id') THEN
    ALTER TABLE attendance_logs ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add to leave_requests
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'leave_requests' AND column_name = 'organization_id') THEN
    ALTER TABLE leave_requests ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add to leave_balances
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'leave_balances' AND column_name = 'organization_id') THEN
    ALTER TABLE leave_balances ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add to breaks
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'breaks' AND column_name = 'organization_id') THEN
    ALTER TABLE breaks ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add to locations
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'locations' AND column_name = 'organization_id') THEN
    ALTER TABLE locations ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
  
  -- Add to time_sessions if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_sessions') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'time_sessions' AND column_name = 'organization_id') THEN
      ALTER TABLE time_sessions ADD COLUMN organization_id UUID REFERENCES organizations(id);
    END IF;
  END IF;
  
  -- Add to screenshots if it exists
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'screenshots') THEN
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'screenshots' AND column_name = 'organization_id') THEN
      ALTER TABLE screenshots ADD COLUMN organization_id UUID REFERENCES organizations(id);
    END IF;
  END IF;
  
  -- Add to fcm_tokens
  IF NOT EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'fcm_tokens' AND column_name = 'organization_id') THEN
    ALTER TABLE fcm_tokens ADD COLUMN organization_id UUID REFERENCES organizations(id);
  END IF;
END $$;

-- Enable RLS on organizations table
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_users_organization_id ON users(organization_id);
CREATE INDEX IF NOT EXISTS idx_users_is_super_admin ON users(is_super_admin);
CREATE INDEX IF NOT EXISTS idx_attendance_logs_organization_id ON attendance_logs(organization_id);
CREATE INDEX IF NOT EXISTS idx_leave_requests_organization_id ON leave_requests(organization_id);
CREATE INDEX IF NOT EXISTS idx_organizations_slug ON organizations(slug);

-- Drop existing user policies to recreate them
DROP POLICY IF EXISTS "Allow authenticated read access" ON users;
DROP POLICY IF EXISTS "Allow self registration" ON users;
DROP POLICY IF EXISTS "Users can view their own profile" ON users;
DROP POLICY IF EXISTS "Managers can view their team members" ON users;

-- Create new multi-tenant RLS policies for users table
CREATE POLICY "super_admin_users_all_access" ON users
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users u 
      WHERE u.id = auth.uid() 
      AND u.is_super_admin = true
    )
  );

CREATE POLICY "organization_users_isolation" ON users
  FOR SELECT TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
    OR auth.uid() = id  -- Users can always see their own profile
  );

CREATE POLICY "users_insert_own" ON users
  FOR INSERT TO authenticated
  WITH CHECK (auth.uid() = id);

CREATE POLICY "users_update_own" ON users
  FOR UPDATE TO authenticated
  USING (auth.uid() = id)
  WITH CHECK (auth.uid() = id);

-- Organizations table policies
CREATE POLICY "super_admin_organizations_all" ON organizations
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "users_view_own_organization" ON organizations
  FOR SELECT TO authenticated
  USING (
    id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid()
    )
  );

-- Update user creation function to handle super admin
CREATE OR REPLACE FUNCTION handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  -- Check if it's a super admin
  IF NEW.email LIKE '%@superadmin.co' THEN
    INSERT INTO users (id, email, full_name, is_super_admin, role)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email), true, 'super_admin')
    ON CONFLICT (id) DO NOTHING;
  ELSE
    -- Regular user - will need organization_id set later
    INSERT INTO users (id, email, full_name)
    VALUES (NEW.id, NEW.email, COALESCE(NEW.raw_user_meta_data->>'full_name', NEW.email))
    ON CONFLICT (id) DO NOTHING;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
