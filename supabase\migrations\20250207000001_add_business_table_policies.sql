/*
  # Multi-Tenant RLS Policies for Business Tables
  
  This migration adds organization-based RLS policies to all business tables
  to ensure data isolation between organizations while allowing super admin access.
*/

-- Drop existing policies and recreate with multi-tenant support

-- Attendance Logs Policies
DROP POLICY IF EXISTS "Users can view their own attendance" ON attendance_logs;
DROP POLICY IF EXISTS "Users can insert their own attendance" ON attendance_logs;
DROP POLICY IF EXISTS "Users can update their own attendance" ON attendance_logs;

CREATE POLICY "super_admin_attendance_all" ON attendance_logs
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "organization_attendance_isolation" ON attendance_logs
  FOR ALL TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
  );

-- Leave Requests Policies
DROP POLICY IF EXISTS "Users can view their own leave requests" ON leave_requests;
DROP POLICY IF EXISTS "Users can insert their own leave requests" ON leave_requests;
DROP POLICY IF EXISTS "Users can update their own leave requests" ON leave_requests;

CREATE POLICY "super_admin_leave_requests_all" ON leave_requests
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "organization_leave_requests_isolation" ON leave_requests
  FOR ALL TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
  );

-- Leave Balances Policies
DROP POLICY IF EXISTS "Users can view their own leave balance" ON leave_balances;
DROP POLICY IF EXISTS "Users can insert their own leave balance" ON leave_balances;
DROP POLICY IF EXISTS "Users can update their own leave balance" ON leave_balances;

CREATE POLICY "super_admin_leave_balances_all" ON leave_balances
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "organization_leave_balances_isolation" ON leave_balances
  FOR ALL TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
  );

-- Breaks Policies
DROP POLICY IF EXISTS "Users can view their own breaks" ON breaks;
DROP POLICY IF EXISTS "Users can insert their own breaks" ON breaks;
DROP POLICY IF EXISTS "Users can update their own breaks" ON breaks;

CREATE POLICY "super_admin_breaks_all" ON breaks
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "organization_breaks_isolation" ON breaks
  FOR ALL TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
  );

-- Locations Policies
DROP POLICY IF EXISTS "Users can view locations" ON locations;
DROP POLICY IF EXISTS "Admins can manage locations" ON locations;

CREATE POLICY "super_admin_locations_all" ON locations
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "organization_locations_isolation" ON locations
  FOR ALL TO authenticated
  USING (
    organization_id IN (
      SELECT organization_id 
      FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = false
    )
  );

-- FCM Tokens Policies (update existing ones)
DROP POLICY IF EXISTS "Users can insert their own FCM tokens" ON fcm_tokens;
DROP POLICY IF EXISTS "Users can view their own FCM tokens" ON fcm_tokens;
DROP POLICY IF EXISTS "Users can update their own FCM tokens" ON fcm_tokens;
DROP POLICY IF EXISTS "Users can delete their own FCM tokens" ON fcm_tokens;

CREATE POLICY "super_admin_fcm_tokens_all" ON fcm_tokens
  FOR ALL TO authenticated
  USING (
    EXISTS (
      SELECT 1 FROM users 
      WHERE id = auth.uid() 
      AND is_super_admin = true
    )
  );

CREATE POLICY "users_own_fcm_tokens" ON fcm_tokens
  FOR ALL TO authenticated
  USING (auth.uid() = user_id)
  WITH CHECK (auth.uid() = user_id);

-- Time Sessions Policies (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_sessions') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "time_sessions_user_policy" ON time_sessions;
    DROP POLICY IF EXISTS "time_sessions_admin_policy" ON time_sessions;
    
    -- Create new policies
    EXECUTE 'CREATE POLICY "super_admin_time_sessions_all" ON time_sessions
      FOR ALL TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM users 
          WHERE id = auth.uid() 
          AND is_super_admin = true
        )
      )';
    
    EXECUTE 'CREATE POLICY "organization_time_sessions_isolation" ON time_sessions
      FOR ALL TO authenticated
      USING (
        organization_id IN (
          SELECT organization_id 
          FROM users 
          WHERE id = auth.uid() 
          AND is_super_admin = false
        )
      )';
  END IF;
END $$;

-- Screenshots Policies (if table exists)
DO $$
BEGIN
  IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'screenshots') THEN
    -- Drop existing policies
    DROP POLICY IF EXISTS "screenshots_user_policy" ON screenshots;
    
    -- Create new policies
    EXECUTE 'CREATE POLICY "super_admin_screenshots_all" ON screenshots
      FOR ALL TO authenticated
      USING (
        EXISTS (
          SELECT 1 FROM users 
          WHERE id = auth.uid() 
          AND is_super_admin = true
        )
      )';
    
    EXECUTE 'CREATE POLICY "organization_screenshots_isolation" ON screenshots
      FOR ALL TO authenticated
      USING (
        organization_id IN (
          SELECT organization_id 
          FROM users 
          WHERE id = auth.uid() 
          AND is_super_admin = false
        )
      )';
  END IF;
END $$;
