import React, { createContext, useContext, useState, ReactNode, useEffect } from 'react';
import { supabase } from './supabase';
import { useAuthStore } from './store';

// Define the shape of the context data
interface UserType {
  chatUsers: any[];

}

// Create the context with a default value
const MyContext = createContext<UserType | undefined>(undefined);

// Create a provider component
interface MyProviderProps {
  children: ReactNode;
}

export const UserProvider: React.FC<MyProviderProps> = ({ children }) => {
  const [chatUsers, setChatUsers] = useState<any[]>([]);
  const currentuser = useAuthStore((state) => state.user);

  async function fetchAllUsers() {
    try {
      // RLS policies will automatically filter users by organization
      // Super admins will see all users, organization users will see only their org users
      const { data: users, error: usersError } = await supabase
        .from('users')
        .select(`
          *,
          organization:organizations(name, slug)
        `);

      if (usersError) throw usersError;

      // Filter out current user from chat list
      let filteredusers = users?.filter((user) => user.id !== currentuser?.id) || [];

      console.log('Filtered users for chat:', filteredusers);
      setChatUsers(filteredusers);
      console.log(`Total users fetched: ${users?.length || 0}`);
    } catch (error) {
      console.error('Error fetching users:', error);
    }
  }

  useEffect(() => {
    if (currentuser) {
      fetchAllUsers();
    }
  }, [currentuser]);

  return (
    <MyContext.Provider value={{ chatUsers }}>
      {children}
    </MyContext.Provider>
  );
};

// Create a custom hook for consuming the context
export const useUserContext = (): UserType => {
  const context = useContext(MyContext);
  if (!context) {
    throw new Error('useUserContext must be used within a UserProvider');
  }
  return context;
};