/*
  # Setup Default Organization for Existing Data
  
  This migration creates a default organization and assigns all existing
  users and data to it, ensuring backward compatibility.
*/

-- Create default organization for existing data
INSERT INTO organizations (name, slug, description, is_active) 
VALUES ('Default Organization', 'default', 'Default organization for existing users', true)
ON CONFLICT (slug) DO NOTHING;

-- Function to update existing data with default organization
DO $$
DECLARE
    default_org_id UUID;
BEGIN
    -- Get the default organization ID
    SELECT id INTO default_org_id FROM organizations WHERE slug = 'default';
    
    -- Update all existing users to belong to default organization (except super admins)
    UPDATE users 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL AND is_super_admin = false;
    
    -- Update all existing attendance logs
    UPDATE attendance_logs 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
    
    -- Update all existing leave requests
    UPDATE leave_requests 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
    
    -- Update all existing leave balances
    UPDATE leave_balances 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
    
    -- Update all existing breaks
    UPDATE breaks 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
    
    -- Update all existing locations
    UPDATE locations 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL;
    
    -- Update FCM tokens based on user's organization
    UPDATE fcm_tokens 
    SET organization_id = default_org_id 
    WHERE organization_id IS NULL 
    AND user_id IN (
        SELECT id FROM users WHERE organization_id = default_org_id
    );
    
    -- Update time_sessions if table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_sessions') THEN
        EXECUTE 'UPDATE time_sessions 
                 SET organization_id = $1 
                 WHERE organization_id IS NULL 
                 AND user_id IN (
                     SELECT id FROM users WHERE organization_id = $1
                 )' USING default_org_id;
    END IF;
    
    -- Update screenshots if table exists
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'screenshots') THEN
        EXECUTE 'UPDATE screenshots 
                 SET organization_id = $1 
                 WHERE organization_id IS NULL 
                 AND user_id IN (
                     SELECT id FROM users WHERE organization_id = $1
                 )' USING default_org_id;
    END IF;
    
    RAISE NOTICE 'Default organization setup completed with ID: %', default_org_id;
END $$;

-- Create a function to automatically set organization_id for new records
CREATE OR REPLACE FUNCTION set_organization_id_from_user()
RETURNS TRIGGER AS $$
BEGIN
    -- If organization_id is not set and user_id exists, get it from user
    IF NEW.organization_id IS NULL AND NEW.user_id IS NOT NULL THEN
        SELECT organization_id INTO NEW.organization_id 
        FROM users 
        WHERE id = NEW.user_id;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically set organization_id
CREATE TRIGGER set_attendance_organization_id
    BEFORE INSERT ON attendance_logs
    FOR EACH ROW
    EXECUTE FUNCTION set_organization_id_from_user();

CREATE TRIGGER set_leave_request_organization_id
    BEFORE INSERT ON leave_requests
    FOR EACH ROW
    EXECUTE FUNCTION set_organization_id_from_user();

CREATE TRIGGER set_leave_balance_organization_id
    BEFORE INSERT ON leave_balances
    FOR EACH ROW
    EXECUTE FUNCTION set_organization_id_from_user();

CREATE TRIGGER set_breaks_organization_id
    BEFORE INSERT ON breaks
    FOR EACH ROW
    EXECUTE FUNCTION set_organization_id_from_user();

CREATE TRIGGER set_fcm_tokens_organization_id
    BEFORE INSERT ON fcm_tokens
    FOR EACH ROW
    EXECUTE FUNCTION set_organization_id_from_user();

-- Create triggers for time_sessions and screenshots if they exist
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'time_sessions') THEN
        EXECUTE 'CREATE TRIGGER set_time_sessions_organization_id
                 BEFORE INSERT ON time_sessions
                 FOR EACH ROW
                 EXECUTE FUNCTION set_organization_id_from_user()';
    END IF;
    
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'screenshots') THEN
        EXECUTE 'CREATE TRIGGER set_screenshots_organization_id
                 BEFORE INSERT ON screenshots
                 FOR EACH ROW
                 EXECUTE FUNCTION set_organization_id_from_user()';
    END IF;
END $$;
