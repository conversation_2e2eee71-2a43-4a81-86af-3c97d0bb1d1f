import { createClient } from '@supabase/supabase-js';
import { config } from 'dotenv';

// Load environment variables
config();

const supabaseUrl = process.env.VITE_SUPABASE_URL;
const supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;

const supabase = createClient(supabaseUrl, supabaseKey);

async function verifyMigrations() {
  console.log('🔍 Verifying Multi-Tenant Migration...\n');

  try {
    // Test 1: Check if organizations table exists
    console.log('1. Testing organizations table...');
    const { data: orgs, error: orgError } = await supabase
      .from('organizations')
      .select('*')
      .limit(1);
    
    if (orgError) {
      console.log('❌ Organizations table not found or not accessible');
      console.log('   Error:', orgError.message);
    } else {
      console.log('✅ Organizations table exists and accessible');
      console.log(`   Found ${orgs?.length || 0} organizations`);
    }

    // Test 2: Check if users table has new columns
    console.log('\n2. Testing users table structure...');
    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, email, organization_id, is_super_admin')
      .limit(1);
    
    if (userError) {
      console.log('❌ Users table structure issue');
      console.log('   Error:', userError.message);
    } else {
      console.log('✅ Users table has required columns');
      if (users && users.length > 0) {
        const user = users[0];
        console.log(`   Sample user: ${user.email}`);
        console.log(`   Has organization_id: ${user.organization_id !== undefined ? 'Yes' : 'No'}`);
        console.log(`   Has is_super_admin: ${user.is_super_admin !== undefined ? 'Yes' : 'No'}`);
      }
    }

    // Test 3: Check if attendance_logs has organization_id
    console.log('\n3. Testing attendance_logs table...');
    const { data: attendance, error: attendanceError } = await supabase
      .from('attendance_logs')
      .select('id, organization_id')
      .limit(1);
    
    if (attendanceError) {
      console.log('❌ Attendance_logs table structure issue');
      console.log('   Error:', attendanceError.message);
    } else {
      console.log('✅ Attendance_logs table has organization_id column');
    }

    // Test 4: Check if leave_requests has organization_id
    console.log('\n4. Testing leave_requests table...');
    const { data: leaves, error: leaveError } = await supabase
      .from('leave_requests')
      .select('id, organization_id')
      .limit(1);
    
    if (leaveError) {
      console.log('❌ Leave_requests table structure issue');
      console.log('   Error:', leaveError.message);
    } else {
      console.log('✅ Leave_requests table has organization_id column');
    }

    // Test 5: Check RLS policies (by trying to access data)
    console.log('\n5. Testing Row Level Security...');
    try {
      const { data: allUsers, error: rlsError } = await supabase
        .from('users')
        .select('count', { count: 'exact', head: true });
      
      if (rlsError) {
        console.log('❌ RLS policies may not be working correctly');
        console.log('   Error:', rlsError.message);
      } else {
        console.log('✅ RLS policies are active and working');
      }
    } catch (error) {
      console.log('❌ RLS test failed:', error.message);
    }

    // Test 6: Check if default organization exists
    console.log('\n6. Testing default organization...');
    const { data: defaultOrg, error: defaultOrgError } = await supabase
      .from('organizations')
      .select('*')
      .eq('slug', 'default')
      .single();
    
    if (defaultOrgError) {
      console.log('❌ Default organization not found');
      console.log('   Error:', defaultOrgError.message);
    } else {
      console.log('✅ Default organization exists');
      console.log(`   Name: ${defaultOrg.name}`);
      console.log(`   ID: ${defaultOrg.id}`);
    }

    console.log('\n🎉 Migration verification complete!');
    console.log('\n📋 Next Steps:');
    console.log('1. Create a super admin user with email ending in @superadmin.co');
    console.log('2. Test login and verify super admin dashboard access');
    console.log('3. Create test organizations and verify data isolation');

  } catch (error) {
    console.error('❌ Verification failed:', error.message);
  }
}

async function createTestSuperAdmin() {
  console.log('\n🔧 Creating test super admin...');
  
  try {
    // Note: This will only work if you have the service role key
    const { data, error } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'testpassword123',
      email_confirm: true
    });

    if (error) {
      console.log('❌ Could not create test super admin (need service role key)');
      console.log('   Error:', error.message);
      console.log('   Manual step: Create user with email ending in @superadmin.co through Supabase Auth');
    } else {
      console.log('✅ Test super admin created');
      console.log('   Email: <EMAIL>');
      console.log('   Password: testpassword123');
    }
  } catch (error) {
    console.log('❌ Super admin creation failed:', error.message);
  }
}

async function main() {
  await verifyMigrations();
  
  // Uncomment the line below if you want to try creating a test super admin
  // await createTestSuperAdmin();
}

main().catch(console.error);
