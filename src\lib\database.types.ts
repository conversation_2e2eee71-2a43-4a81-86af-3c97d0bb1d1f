export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type UserRole = 'super_admin' | 'admin' | 'manager' | 'employee'
export type WorkMode = 'on_site' | 'remote'
export type LeaveType = 'annual' | 'sick' | 'personal'
export type LeaveStatus = 'pending' | 'approved' | 'rejected'

export interface Database {
  public: {
    Tables: {
      organizations: {
        Row: {
          id: string
          name: string
          slug: string
          description?: string
          logo_url?: string
          is_active: boolean
          created_by?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          slug: string
          description?: string
          logo_url?: string
          is_active?: boolean
          created_by?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          slug?: string
          description?: string
          logo_url?: string
          is_active?: boolean
          created_by?: string
          created_at?: string
          updated_at?: string
        }
      }
      users: {
        Row: {
          id: string
          email: string
          full_name: string
          role: UserRole
          organization_id?: string
          is_super_admin: boolean
          department?: string
          manager_id?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id: string
          email: string
          full_name: string
          role?: UserRole
          organization_id?: string
          is_super_admin?: boolean
          department?: string
          manager_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          email?: string
          full_name?: string
          role?: UserRole
          organization_id?: string
          is_super_admin?: boolean
          department?: string
          manager_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      tasks_of_projects: {
        Row: {
          id: string
          title: string
          description?: string
          status: 'todo' | 'inProgress' | 'review' | 'done'
          score: number
          priority?: string
          project_id: string
          created_at: string
          action_date?: string
          deadline?: string
          imageurl?: string
          devops?: Json
          // Add other task fields as needed
        }
        Insert: {
          id?: string
          title: string
          description?: string
          status?: 'todo' | 'inProgress' | 'review' | 'done'
          score?: number
          priority?: string
          project_id: string
          created_at?: string
          action_date?: string
          deadline?: string
          imageurl?: string
          devops?: Json
        }
        Update: {
          id?: string
          title?: string
          description?: string
          status?: 'todo' | 'inProgress' | 'review' | 'done'
          score?: number
          priority?: string
          project_id?: string
          created_at?: string
          action_date?: string
          deadline?: string
          imageurl?: string
          devops?: Json
        }
      }
      projects: {
        Row: {
          id: string
          name: string
          description?: string
          created_at: string
          created_by: string
          devops?: Json
          // Add other project fields as needed
        }
        Insert: {
          id?: string
          name: string
          description?: string
          created_at?: string
          created_by: string
          devops?: Json
        }
        Update: {
          id?: string
          name?: string
          description?: string
          created_at?: string
          created_by?: string
          devops?: Json
        }
      }
      comments: {
        Row: {
          comment_id: string
          task_id: string
          user_id: string
          comment_text: string
          created_at: string
        }
        Insert: {
          comment_id?: string
          task_id: string
          user_id: string
          comment_text: string
          created_at?: string
        }
        Update: {
          comment_id?: string
          task_id?: string
          user_id?: string
          comment_text?: string
          created_at?: string
        }
      }
      attendance_logs: {
        Row: {
          id: string
          user_id: string
          organization_id?: string
          check_in_time: string
          check_out_time?: string
          work_mode: WorkMode
          location_id?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          organization_id?: string
          check_in_time: string
          check_out_time?: string
          work_mode: WorkMode
          location_id?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          organization_id?: string
          check_in_time?: string
          check_out_time?: string
          work_mode?: WorkMode
          location_id?: string
          created_at?: string
          updated_at?: string
        }
      }
      leave_requests: {
        Row: {
          id: string
          user_id: string
          organization_id?: string
          leave_type: LeaveType
          start_date: string
          end_date: string
          days_requested: number
          reason?: string
          status: LeaveStatus
          approved_by?: string
          approved_at?: string
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          user_id: string
          organization_id?: string
          leave_type: LeaveType
          start_date: string
          end_date: string
          days_requested: number
          reason?: string
          status?: LeaveStatus
          approved_by?: string
          approved_at?: string
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          user_id?: string
          organization_id?: string
          leave_type?: LeaveType
          start_date?: string
          end_date?: string
          days_requested?: number
          reason?: string
          status?: LeaveStatus
          approved_by?: string
          approved_at?: string
          created_at?: string
          updated_at?: string
        }
      }
      locations: {
        Row: {
          id: string
          name: string
          address: string
          organization_id?: string
          latitude?: number
          longitude?: number
          radius?: number
          created_at: string
          updated_at: string
        }
        Insert: {
          id?: string
          name: string
          address: string
          organization_id?: string
          latitude?: number
          longitude?: number
          radius?: number
          created_at?: string
          updated_at?: string
        }
        Update: {
          id?: string
          name?: string
          address?: string
          organization_id?: string
          latitude?: number
          longitude?: number
          radius?: number
          created_at?: string
          updated_at?: string
        }
      }
      // Add other tables as needed
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
