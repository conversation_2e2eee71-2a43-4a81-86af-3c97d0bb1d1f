import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../lib/AuthProvider';
import { useOrganizationContext } from '../hooks/useOrganizationContext';

interface PrivateRouteProps {
  children: React.ReactNode;
  adminOnly?: boolean;
  superAdminOnly?: boolean;
  managerOnly?: boolean;
  requireOrganization?: boolean;
}

const PrivateRoute: React.FC<PrivateRouteProps> = ({
  children,
  adminOnly = false,
  superAdminOnly = false,
  managerOnly = false,
  requireOrganization = false
}) => {
  const { user, loading: authLoading } = useAuth();
  const { currentUser, organization, isLoading: contextLoading } = useOrganizationContext();

  // Show loading while authentication or context is loading
  if (authLoading || contextLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  // Redirect to login if not authenticated
  if (!user) {
    return <Navigate to="/login" replace />;
  }

  // Redirect if user data is not loaded
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  // Check super admin access
  if (superAdminOnly && currentUser.role !== 'superadmin') {
    return <Navigate to="/" replace />;
  }

  // Check admin access (superadmin can access admin routes)
  if (
    adminOnly &&
    currentUser.role !== 'superadmin' &&
    currentUser.role !== 'admin'
  ) {
    return <Navigate to="/" replace />;
  }

  // Check manager access (superadmin and admin can access manager routes)
  if (
    managerOnly &&
    !['superadmin', 'admin', 'manager'].includes(currentUser.role)
  ) {
    return <Navigate to="/" replace />;
  }

  // Check if organization is required (superadmin doesn't need organization)
  if (
    requireOrganization &&
    currentUser.role !== 'superadmin' &&
    !organization
  ) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">No Organization</h2>
          <p className="text-gray-600 mb-4">
            You are not assigned to any organization. Please contact your administrator.
          </p>
          <button
            onClick={() => {
              localStorage.clear();
              window.location.href = '/login';
            }}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Back to Login
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default PrivateRoute;