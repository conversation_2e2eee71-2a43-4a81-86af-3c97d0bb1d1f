import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Building2,
  Calendar,
  Clock,
  Activity
} from 'lucide-react';
import { supabase } from '../lib/supabase';

interface AnalyticsData {
  totalOrganizations: number;
  totalUsers: number;
  totalAttendanceToday: number;
  totalProjects: number;
  usersByRole: any[];
  recentActivity: any[];
}

const GlobalAnalytics: React.FC = () => {
  const [data, setData] = useState<AnalyticsData>({
    totalOrganizations: 0,
    totalUsers: 0,
    totalAttendanceToday: 0,
    totalProjects: 0,
    usersByRole: [],
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    fetchAnalyticsData();
  }, []);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);

      // Fetch basic counts
      const [
        { count: orgCount },
        { count: userCount },
        { count: projectCount }
      ] = await Promise.all([
        supabase.from('organizations').select('*', { count: 'exact', head: true }),
        supabase.from('users').select('*', { count: 'exact', head: true }),
        supabase.from('projects').select('*', { count: 'exact', head: true })
      ]);

      // Fetch today's attendance
      const today = new Date().toISOString().split('T')[0];
      const { count: attendanceCount } = await supabase
        .from('attendance_logs')
        .select('*', { count: 'exact', head: true })
        .gte('check_in_time', today);

      // Fetch users by role
      const { data: roleData } = await supabase
        .from('users')
        .select('role')
        .not('is_super_admin', 'eq', true);

      const usersByRole = roleData?.reduce((acc: any, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {}) || {};

      setData({
        totalOrganizations: orgCount || 0,
        totalUsers: userCount || 0,
        totalAttendanceToday: attendanceCount || 0,
        totalProjects: projectCount || 0,
        usersByRole: Object.entries(usersByRole).map(([role, count]) => ({ role, count })),
        recentActivity: []
      });

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div>
        <h1 className="text-2xl font-bold text-gray-900">Global Analytics</h1>
        <p className="text-gray-600">System-wide metrics and insights</p>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building2 className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Organizations</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalOrganizations}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalUsers}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Attendance Today</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalAttendanceToday}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Projects</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalProjects}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Users by Role Chart */}
      <div className="bg-white shadow rounded-lg p-6">
        <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
          <BarChart3 className="mr-2 h-5 w-5" />
          Users by Role
        </h3>
        <div className="space-y-3">
          {data.usersByRole.map((item, index) => (
            <div key={item.role} className="flex items-center justify-between">
              <div className="flex items-center">
                <div className={`w-3 h-3 rounded-full mr-3 ${
                  ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'][index % 4]
                }`}></div>
                <span className="text-sm font-medium text-gray-900 capitalize">{item.role}</span>
              </div>
              <span className="text-sm text-gray-500">{item.count}</span>
            </div>
          ))}
        </div>
      </div>

      {/* System Overview */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            System Overview
          </h3>
          <div className="text-center py-8">
            <BarChart3 className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">Advanced Analytics</h3>
            <p className="mt-1 text-sm text-gray-500">
              Detailed analytics and reporting features coming soon...
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlobalAnalytics;
