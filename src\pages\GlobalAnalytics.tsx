import React, { useState, useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Building2,
  Calendar,
  Clock,
  Activity,
  PieChart
} from 'lucide-react';
import { supabase } from '../lib/supabase';

interface AnalyticsData {
  totalOrganizations: number;
  totalUsers: number;
  totalAttendanceToday: number;
  totalProjects: number;
  organizationGrowth: any[];
  usersByRole: any[];
  attendanceByOrganization: any[];
  recentActivity: any[];
}

const GlobalAnalytics: React.FC = () => {
  const [data, setData] = useState<AnalyticsData>({
    totalOrganizations: 0,
    totalUsers: 0,
    totalAttendanceToday: 0,
    totalProjects: 0,
    organizationGrowth: [],
    usersByRole: [],
    attendanceByOrganization: [],
    recentActivity: []
  });
  const [loading, setLoading] = useState(true);
  const [timeRange, setTimeRange] = useState('7d'); // 7d, 30d, 90d

  useEffect(() => {
    fetchAnalyticsData();
  }, [timeRange]);

  const fetchAnalyticsData = async () => {
    try {
      setLoading(true);

      // Fetch basic counts
      const [
        { count: orgCount },
        { count: userCount },
        { count: projectCount }
      ] = await Promise.all([
        supabase.from('organizations').select('*', { count: 'exact', head: true }),
        supabase.from('users').select('*', { count: 'exact', head: true }),
        supabase.from('projects').select('*', { count: 'exact', head: true })
      ]);

      // Fetch today's attendance
      const today = new Date().toISOString().split('T')[0];
      const { count: attendanceCount } = await supabase
        .from('attendance_logs')
        .select('*', { count: 'exact', head: true })
        .gte('check_in_time', today);

      // Fetch users by role
      const { data: roleData } = await supabase
        .from('users')
        .select('role')
        .not('is_super_admin', 'eq', true);

      const usersByRole = roleData?.reduce((acc: any, user) => {
        acc[user.role] = (acc[user.role] || 0) + 1;
        return acc;
      }, {}) || {};

      // Fetch organization growth (last 30 days)
      const thirtyDaysAgo = new Date();
      thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
      
      const { data: orgGrowthData } = await supabase
        .from('organizations')
        .select('created_at')
        .gte('created_at', thirtyDaysAgo.toISOString())
        .order('created_at');

      // Group by date
      const organizationGrowth = orgGrowthData?.reduce((acc: any, org) => {
        const date = new Date(org.created_at).toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {}) || {};

      // Fetch attendance by organization
      const { data: attendanceByOrgData } = await supabase
        .from('attendance_logs')
        .select(`
          organization_id,
          organizations(name)
        `)
        .gte('check_in_time', today);

      const attendanceByOrganization = attendanceByOrgData?.reduce((acc: any, log) => {
        const orgName = log.organizations?.name || 'Unknown';
        acc[orgName] = (acc[orgName] || 0) + 1;
        return acc;
      }, {}) || {};

      // Fetch recent activity (recent users, organizations)
      const { data: recentUsers } = await supabase
        .from('users')
        .select('full_name, email, created_at, organizations(name)')
        .order('created_at', { ascending: false })
        .limit(5);

      const { data: recentOrgs } = await supabase
        .from('organizations')
        .select('name, created_at')
        .order('created_at', { ascending: false })
        .limit(5);

      const recentActivity = [
        ...(recentUsers?.map(user => ({
          type: 'user',
          name: user.full_name,
          detail: user.organizations?.name || 'No organization',
          timestamp: user.created_at
        })) || []),
        ...(recentOrgs?.map(org => ({
          type: 'organization',
          name: org.name,
          detail: 'New organization',
          timestamp: org.created_at
        })) || [])
      ].sort((a, b) => new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()).slice(0, 10);

      setData({
        totalOrganizations: orgCount || 0,
        totalUsers: userCount || 0,
        totalAttendanceToday: attendanceCount || 0,
        totalProjects: projectCount || 0,
        organizationGrowth: Object.entries(organizationGrowth).map(([date, count]) => ({ date, count })),
        usersByRole: Object.entries(usersByRole).map(([role, count]) => ({ role, count })),
        attendanceByOrganization: Object.entries(attendanceByOrganization).map(([org, count]) => ({ org, count })),
        recentActivity
      });

    } catch (error) {
      console.error('Error fetching analytics data:', error);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Global Analytics</h1>
          <p className="text-gray-600">System-wide metrics and insights</p>
        </div>
        <select
          value={timeRange}
          onChange={(e) => setTimeRange(e.target.value)}
          className="border border-gray-300 rounded-md px-3 py-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="7d">Last 7 days</option>
          <option value="30d">Last 30 days</option>
          <option value="90d">Last 90 days</option>
        </select>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Building2 className="h-8 w-8 text-blue-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Organizations</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalOrganizations}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Users className="h-8 w-8 text-green-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Users</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalUsers}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <Clock className="h-8 w-8 text-purple-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Attendance Today</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalAttendanceToday}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>

        <div className="bg-white overflow-hidden shadow rounded-lg">
          <div className="p-5">
            <div className="flex items-center">
              <div className="flex-shrink-0">
                <TrendingUp className="h-8 w-8 text-orange-500" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 truncate">Total Projects</dt>
                  <dd className="text-lg font-medium text-gray-900">{data.totalProjects}</dd>
                </dl>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Charts Row */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Users by Role */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <PieChart className="mr-2 h-5 w-5" />
            Users by Role
          </h3>
          <div className="space-y-3">
            {data.usersByRole.map((item, index) => (
              <div key={item.role} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500'][index % 4]
                  }`}></div>
                  <span className="text-sm font-medium text-gray-900 capitalize">{item.role}</span>
                </div>
                <span className="text-sm text-gray-500">{item.count}</span>
              </div>
            ))}
          </div>
        </div>

        {/* Attendance by Organization */}
        <div className="bg-white shadow rounded-lg p-6">
          <h3 className="text-lg font-medium text-gray-900 mb-4 flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Today's Attendance by Organization
          </h3>
          <div className="space-y-3">
            {data.attendanceByOrganization.slice(0, 5).map((item, index) => (
              <div key={item.org} className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className={`w-3 h-3 rounded-full mr-3 ${
                    ['bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500', 'bg-red-500'][index % 5]
                  }`}></div>
                  <span className="text-sm font-medium text-gray-900">{item.org}</span>
                </div>
                <span className="text-sm text-gray-500">{item.count}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white shadow rounded-lg">
        <div className="px-4 py-5 sm:p-6">
          <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4 flex items-center">
            <Activity className="mr-2 h-5 w-5" />
            Recent Activity
          </h3>
          <div className="flow-root">
            <ul className="-mb-8">
              {data.recentActivity.map((activity, index) => (
                <li key={index}>
                  <div className="relative pb-8">
                    {index !== data.recentActivity.length - 1 && (
                      <span className="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200" />
                    )}
                    <div className="relative flex space-x-3">
                      <div>
                        <span className={`h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white ${
                          activity.type === 'user' ? 'bg-blue-500' : 'bg-green-500'
                        }`}>
                          {activity.type === 'user' ? (
                            <Users className="h-4 w-4 text-white" />
                          ) : (
                            <Building2 className="h-4 w-4 text-white" />
                          )}
                        </span>
                      </div>
                      <div className="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                        <div>
                          <p className="text-sm text-gray-500">
                            <span className="font-medium text-gray-900">{activity.name}</span>{' '}
                            {activity.type === 'user' ? 'joined' : 'was created'}
                          </p>
                          <p className="text-xs text-gray-400">{activity.detail}</p>
                        </div>
                        <div className="text-right text-sm whitespace-nowrap text-gray-500">
                          <time>{new Date(activity.timestamp).toLocaleDateString()}</time>
                        </div>
                      </div>
                    </div>
                  </div>
                </li>
              ))}
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GlobalAnalytics;
