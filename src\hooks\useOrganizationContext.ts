import { useState, useEffect } from 'react';
import { supabase } from '../lib/supabase';
import { useAuthStore } from '../lib/store';
import type { Database } from '../lib/database.types';

type Organization = Database['public']['Tables']['organizations']['Row'];
type User = Database['public']['Tables']['users']['Row'];

interface OrganizationContextType {
  currentUser: User | null;
  organization: Organization | null;
  isSuperAdmin: boolean;
  isLoading: boolean;
  error: string | null;
  refreshContext: () => Promise<void>;
}

export const useOrganizationContext = (): OrganizationContextType => {
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isSuperAdmin, setIsSuperAdmin] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const authUser = useAuthStore((state) => state.user);

  const fetchUserContext = async () => {
    if (!authUser) {
      setCurrentUser(null);
      setOrganization(null);
      setIsSuperAdmin(false);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      // Fetch user with organization data
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select(`
          *,
          organization:organizations(*)
        `)
        .eq('id', authUser.id)
        .single();

      if (userError) {
        console.error('User data fetch error:', userError);

        // If user doesn't exist in users table, create a basic profile
        if (userError.code === 'PGRST116') { // No rows returned
          const isSuper = authUser.email?.endsWith('@superadmin.co') || false;
          const { error: insertError } = await supabase
            .from('users')
            .insert({
              id: authUser.id,
              email: authUser.email || '',
              full_name: authUser.email || 'User',
              role: isSuper ? 'super_admin' : 'employee',
              is_super_admin: isSuper,
            });

          if (insertError) {
            console.error('Error creating user profile:', insertError);
            throw insertError;
          }

          // Retry fetching after creation
          const { data: newUserData, error: retryError } = await supabase
            .from('users')
            .select(`
              *,
              organization:organizations(*)
            `)
            .eq('id', authUser.id)
            .single();

          if (retryError) {
            throw retryError;
          }

          setCurrentUser(newUserData);
          setOrganization(newUserData?.organization || null);
          setIsSuperAdmin(newUserData?.is_super_admin || false);
        } else {
          throw userError;
        }
      } else {
        setCurrentUser(userData);
        setOrganization(userData?.organization || null);
        setIsSuperAdmin(userData?.is_super_admin || false);
      }

      // Update localStorage with current context (safely)
      const currentUserData = userData || currentUser;
      if (currentUserData) {
        localStorage.setItem('user_role', currentUserData.role || 'employee');
        localStorage.setItem('is_super_admin', (currentUserData.is_super_admin || false).toString());

        if (currentUserData.organization_id) {
          localStorage.setItem('organization_id', currentUserData.organization_id);
        } else {
          localStorage.removeItem('organization_id');
        }
      }

    } catch (err) {
      console.error('Error fetching user context:', err);
      setError(err instanceof Error ? err.message : 'Failed to load user context');

      // Set fallback values
      setCurrentUser(null);
      setOrganization(null);
      setIsSuperAdmin(false);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchUserContext();
  }, [authUser]);

  return {
    currentUser,
    organization,
    isSuperAdmin,
    isLoading,
    error,
    refreshContext: fetchUserContext
  };
};

// Helper hook for organization-aware data fetching
export const useOrganizationData = () => {
  const { currentUser, isSuperAdmin } = useOrganizationContext();

  const fetchOrganizationUsers = async () => {
    const { data, error } = await supabase
      .from('users')
      .select('*');

    return { data, error };
  };

  const fetchOrganizationAttendance = async (startDate?: string, endDate?: string) => {
    let query = supabase
      .from('attendance_logs')
      .select(`
        *,
        user:users(*)
      `);

    if (startDate) {
      query = query.gte('check_in_time', startDate);
    }
    if (endDate) {
      query = query.lte('check_in_time', endDate);
    }

    const { data, error } = await query;
    return { data, error };
  };

  const fetchOrganizationLeaveRequests = async () => {
    const { data, error } = await supabase
      .from('leave_requests')
      .select(`
        *,
        user:users(*)
      `);

    return { data, error };
  };

  const fetchOrganizationProjects = async () => {
    const { data, error } = await supabase
      .from('projects')
      .select('*');

    return { data, error };
  };

  const fetchOrganizationTasks = async () => {
    const { data, error } = await supabase
      .from('tasks_of_projects')
      .select(`
        *,
        project:projects(*)
      `);

    return { data, error };
  };

  return {
    fetchOrganizationUsers,
    fetchOrganizationAttendance,
    fetchOrganizationLeaveRequests,
    fetchOrganizationProjects,
    fetchOrganizationTasks,
    isSuperAdmin,
    currentUser
  };
};

// Helper function to check if user has permission for specific actions
export const usePermissions = () => {
  const { currentUser, isSuperAdmin } = useOrganizationContext();

  const canManageOrganizations = isSuperAdmin;
  const canManageUsers = isSuperAdmin || currentUser?.role === 'admin';
  const canManageProjects = isSuperAdmin || ['admin', 'manager'].includes(currentUser?.role || '');
  const canViewAllData = isSuperAdmin || currentUser?.role === 'admin';
  const canApproveLeave = isSuperAdmin || ['admin', 'manager'].includes(currentUser?.role || '');

  return {
    canManageOrganizations,
    canManageUsers,
    canManageProjects,
    canViewAllData,
    canApproveLeave,
    isSuperAdmin,
    userRole: currentUser?.role
  };
};
